{"name": "box", "version": "1.6.34", "private": true, "dependencies": {"@functionland/fula-sec": "*", "@functionland/react-native-fula": "*", "@gorhom/bottom-sheet": "*", "@metamask/sdk-react": "*", "@notifee/react-native": "*", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-clipboard/clipboard": "*", "@react-native-community/netinfo": "9.3.10", "@react-native-community/slider": "4.4.2", "@react-native-firebase/app": "*", "@react-native-firebase/crashlytics": "*", "@react-native-picker/picker": "2.4.10", "@react-native/metro-config": "*", "@react-navigation/bottom-tabs": "*", "@react-navigation/material-top-tabs": "*", "@react-navigation/native": "*", "@react-navigation/native-stack": "*", "@react-navigation/stack": "*", "@react-spring/three": "*", "@react-three/fiber": "*", "@shopify/react-native-skia": "0.1.196", "@shopify/restyle": "*", "@testing-library/jest-native": "*", "@testing-library/react-native": "*", "@visx/scale": "*", "@walletconnect/encoding": "*", "@walletconnect/react-native-compat": "*", "@web3modal/wagmi-react-native": "*", "axios": "*", "big-integer": "*", "expo": "*", "expo-gl": "~13.0.1", "expo-three": "*", "lodash": "*", "metro-config": "*", "moment": "*", "node-libs-react-native": "*", "react": "*", "react-content-loader": "*", "react-native": "0.72.10", "react-native-background-timer": "*", "react-native-ble-manager": "*", "react-native-config": "*", "react-native-device-info": "*", "react-native-elements": "*", "react-native-gesture-handler": "~2.12.0", "react-native-get-random-values": "~1.9.0", "react-native-keyboard-aware-scroll-view": "*", "react-native-keychain": "*", "react-native-localize": "*", "react-native-modal": "*", "react-native-pager-view": "6.2.0", "react-native-paper": "*", "react-native-randombytes": "*", "react-native-reanimated": "~3.3.0", "react-native-reanimated-carousel": "*", "react-native-redash": "*", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-svg-transformer": "*", "react-native-tab-view": "*", "react-native-url-polyfill": "*", "react-native-webview": "13.2.2", "react-native-wheel-color-picker": "*", "react-native-wifi-reborn": "*", "react-native-zeroconf": "*", "text-encoding-polyfill": "*", "three": "*", "viem": "*", "wagmi": "*", "zustand": "*", "@types/react-native-background-timer": "*", "ethers": "*", "whatwg-fetch": "*", "react-native-permissions": "*", "base-64": "*", "react-native-syntax-highlighter": "*", "react-syntax-highlighter": "*", "i18next": "*", "react-i18next": "*", "i18next-resources-to-backend": "*"}, "devDependencies": {"@types/react-native-background-timer": "^2.0.2"}}